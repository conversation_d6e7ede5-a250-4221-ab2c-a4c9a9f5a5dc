import { supabase } from '@/lib/supabase-enhanced';

export interface UpdateProfileData {
  name?: string;
  phone_number?: string;
  email?: string;
}

export class ProfileService {
  /**
   * Update user profile information
   */
  static async updateProfile(userId: string, data: UpdateProfileData) {
    const { data: updatedProfile, error } = await supabase
      .from('users')
      .update({
        ...data,
        updated_at: new Date().toISOString(),
      })
      .eq('id', userId)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update profile: ${error.message}`);
    }

    return updatedProfile;
  }

  /**
   * Get user profile by ID
   */
  static async getProfile(userId: string) {
    const { data: profile, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (error) {
      throw new Error(`Failed to get profile: ${error.message}`);
    }

    return profile;
  }
}