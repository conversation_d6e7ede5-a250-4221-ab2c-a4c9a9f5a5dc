'use client';

import { Button } from '@/components/ui/button';
import { PmaForm } from '@/features/pma-management/components/pma-form';
import { useCreatePMACertificate } from '@/features/pma-management/hooks/use-create-pma-certificate';
import { uploadToOBS } from '@/lib/obs-upload';
import { useProjectContext } from '@/providers/project-context';
import { FileText } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useParams, useRouter } from 'next/navigation';
import { useState } from 'react';

export default function AddPma() {
  const { selectedProjectId } = useProjectContext();
  const { mutate, isPending } = useCreatePMACertificate();
  const [isUploading, setIsUploading] = useState(false);
  const router = useRouter();
  const params = useParams();
  const locale = params.locale as string;
  const t = useTranslations('pmaManagement.pages.add');
  const tNav = useTranslations('navigation');

  const handleSubmit = async (
    data:
      | {
          pmas: {
            location: string;
            pmaNumber: string;
            inspectionDate: string;
            liftInstallationDate: string;
            competentPersonId: string;
            pmaExpiryDate: string;
            certificateFile?: File;
          }[];
        }
      | {
          pmas: {
            pmaExpiryDate: string;
            location?: string;
            pmaNumber?: string;
            inspectionDate?: string;
            liftInstallationDate?: string;
            competentPersonId?: string;
            certificateFile?: File;
          }[];
        },
  ) => {
    if (!selectedProjectId) {
      console.error('Project ID is not selected.');
      return;
    }

    setIsUploading(true);

    try {
      // Process each PMA entry
      for (const pma of data.pmas) {
        // Validate required fields for add mode
        if (!pma.pmaNumber || !pma.location || !pma.competentPersonId) {
          console.error('Required fields are missing for PMA entry');
          continue;
        }

        let fileUrl: string | undefined;

        // Upload PDF file if provided
        if (pma.certificateFile) {
          try {
            fileUrl = await uploadToOBS({
              file: pma.certificateFile,
              folder: `projects/${selectedProjectId}/pma-certificates`,
            });
          } catch (error) {
            console.error('Failed to upload PDF:', error);
            // Continue without file URL if upload fails
          }
        }

        // Create PMA certificate with all the new fields
        mutate({
          project_id: selectedProjectId,
          pma_number: pma.pmaNumber,
          location: pma.location,
          expiry_date: pma.pmaExpiryDate,
          inspection_date: pma.inspectionDate,
          lift_installation_date: pma.liftInstallationDate,
          status: 'valid',
          competent_person_id: pma.competentPersonId,
          file_url: fileUrl,
        });
      }

      router.push(`/${locale}/pmas`);
    } catch (error) {
      console.error('Error processing PMA entries:', error);
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="bg-gradient-to-br from-slate-50 via-white to-slate-50/50">
      {/* Enhanced Header with Breadcrumbs */}
      <div className="sticky top-0 z-40 bg-white/80 backdrop-blur-lg border-b border-slate-200/60">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            {/* Breadcrumbs */}
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Link
                href="/pmas"
                className="text-muted-foreground hover:text-foreground transition-colors"
              >
                <div className="flex items-center space-x-2">
                  <FileText className="h-4 w-4" />
                  <span>{tNav('pmas')}</span>
                </div>
              </Link>
              <span className="text-muted-foreground">/</span>
              <span className="text-primary font-medium">
                {t('breadcrumb')}
              </span>
            </div>

            {/* Actions */}
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                onClick={() => router.push(`/${locale}/pmas`)}
              >
                {t('cancel')}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 py-8">
        <div className="max-w-2xl mx-auto">
          {/* Header */}
          <div className="mb-8 text-center">
            <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
              {t('title')}
            </h1>
            <p className="mt-2 text-lg text-muted-foreground">
              {t('description')}
            </p>
          </div>

          {/* Form */}
          <PmaForm
            onSubmit={handleSubmit}
            isLoading={isPending || isUploading}
          />
        </div>
      </div>
    </div>
  );
}
